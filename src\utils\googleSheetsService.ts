// Google Apps Script Webhook Configuration Interface
export interface GoogleSheetsConfig {
  webhookUrl: string;
  spreadsheetId: string;
  worksheetName: string;
  apiKey?: string; // Optional API key for additional security
}

// Booking Data Interface for Google Sheets
export interface BookingDataForSheets {
  bookingReference: string;
  submissionTime: string;
  eventType: string;
  packageName: string;
  packagePrice: string;
  brideName: string;
  groomName: string;
  phoneNumber: string;
  email: string;
  eventDate: string;
  venue: string;
  guestCount: string;
  makeupArtist: string;
  additionalNotes: string;
  hearAbout: string;
  // Additional fields for different event types
  registrationTime?: string;
  ringExchangeTime?: string;
  ceremonyTime?: string;
  eventEndTime?: string;
  primaryLocation?: string;
  weddingDate?: string;
  homecomingDate?: string;
  preShootDate?: string;
  outfitChanges?: string;
  desiredLocation?: string;
  weddingVenue?: string;
  weddingGuestCount?: string;
  homecomingVenue?: string;
  homecomingGuestCount?: string;
  homecomingEndTime?: string;
}

// Google Sheets Service Class using Google Apps Script Webhook
export class GoogleSheetsService {
  private config: GoogleSheetsConfig;

  constructor(config: GoogleSheetsConfig) {
    this.config = config;
    this.validateConfig();
  }

  private validateConfig() {
    if (!this.config.webhookUrl) {
      throw new Error('Google Apps Script webhook URL is required');
    }
    if (!this.config.spreadsheetId) {
      throw new Error('Google Sheets spreadsheet ID is required');
    }
    console.log('Google Sheets service initialized successfully');
  }

  // Send data to Google Apps Script webhook
  private async sendToWebhook(data: any): Promise<any> {
    try {
      const payload = {
        action: 'appendBookingData',
        spreadsheetId: this.config.spreadsheetId,
        worksheetName: this.config.worksheetName,
        data: data,
        apiKey: this.config.apiKey
      };

      const response = await fetch(this.config.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error sending data to Google Apps Script webhook:', error);
      throw error;
    }
  }

  // Append booking data to Google Sheets via webhook
  async appendBookingData(bookingData: BookingDataForSheets): Promise<void> {
    try {
      const response = await this.sendToWebhook(bookingData);

      if (response.success) {
        console.log('Booking data successfully added to Google Sheets:', response);
        return response;
      } else {
        throw new Error(response.error || 'Failed to add data to Google Sheets');
      }
    } catch (error) {
      console.error('Error appending booking data to Google Sheets:', error);
      throw error;
    }
  }

  // Test the webhook connection
  async testConnection(): Promise<boolean> {
    try {
      const testPayload = {
        action: 'testConnection',
        spreadsheetId: this.config.spreadsheetId,
        worksheetName: this.config.worksheetName,
        apiKey: this.config.apiKey
      };

      const response = await fetch(this.config.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testPayload)
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Google Sheets webhook connection test successful:', result);
        return result.success || true;
      } else {
        console.error('Google Sheets webhook connection test failed:', response.status);
        return false;
      }
    } catch (error) {
      console.error('Google Sheets webhook connection test failed:', error);
      return false;
    }
  }
}

// Utility function to transform form data to Google Sheets format
export function transformFormDataForSheets(
  formData: any,
  packageData: any,
  bookingReference: string,
  eventOptions: any[]
): BookingDataForSheets {
  const eventType = eventOptions.find(e => e.value === formData.eventType)?.label || formData.eventType;
  const packageInfo = packageData[formData.eventType]?.[formData.package];
  const packagePrice = packageInfo?.price || 'N/A';

  return {
    bookingReference,
    submissionTime: new Date().toLocaleString(),
    eventType,
    packageName: formData.package,
    packagePrice,
    brideName: formData.brideAName,
    groomName: formData.groomName,
    phoneNumber: formData.phoneNumber,
    email: formData.email,
    eventDate: formData.eventDate || formData.weddingDate || formData.homecomingDate || formData.preShootDate,
    venue: formData.venue || formData.weddingVenue || formData.homecomingVenue,
    guestCount: formData.guestCount || formData.weddingGuestCount || formData.homecomingGuestCount,
    makeupArtist: formData.makeupArtist,
    additionalNotes: formData.additionalNotes,
    hearAbout: formData.hearAbout,
    // Event-specific fields
    registrationTime: formData.registrationTime,
    ringExchangeTime: formData.ringExchangeTime,
    ceremonyTime: formData.ceremonyTime,
    eventEndTime: formData.eventEndTime,
    primaryLocation: formData.primaryLocation,
    weddingDate: formData.weddingDate,
    homecomingDate: formData.homecomingDate,
    preShootDate: formData.preShootDate,
    outfitChanges: formData.outfitChanges,
    desiredLocation: formData.desiredLocation,
    weddingVenue: formData.weddingVenue,
    weddingGuestCount: formData.weddingGuestCount,
    homecomingVenue: formData.homecomingVenue,
    homecomingGuestCount: formData.homecomingGuestCount,
    homecomingEndTime: formData.homecomingEndTime
  };
}
