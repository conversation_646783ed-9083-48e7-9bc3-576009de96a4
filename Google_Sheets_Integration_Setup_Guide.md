# Google Sheets Integration Setup Guide
## Professional Photography Booking System

This guide will walk you through setting up Google Sheets integration for your booking system using Google Apps Script, allowing automatic data logging of all booking submissions.

---

## 📋 **Overview**

The Google Sheets integration automatically saves all booking form submissions to a Google Sheets spreadsheet when users click "Submit via WhatsApp". This provides:

- **Centralized Data Storage**: All bookings in one organized spreadsheet
- **Real-time Updates**: Data appears instantly after form submission
- **Easy Analysis**: Use Google Sheets features for reporting and analytics
- **Backup & Security**: Cloud-based storage with Google's security
- **Team Access**: Share spreadsheet with team members
- **Browser Compatible**: Uses Google Apps Script webhook (no server required)

---

## 🚀 **Step 1: Create Google Sheets Spreadsheet**

### 1.1 Create New Spreadsheet
1. Go to [Google Sheets](https://sheets.google.com/)
2. Click "Blank" to create new spreadsheet
3. Rename it to: `Photography Bookings - [Your Business Name]`

### 1.2 Set Up Worksheet
1. Rename the first sheet to: `Bookings`
2. The Google Apps Script will automatically create headers when first booking is submitted
3. **Optional**: You can leave the spreadsheet empty - headers will be added automatically

### 1.3 Get Spreadsheet ID
1. Copy the spreadsheet URL
2. Extract the ID from the URL:
   ```
   https://docs.google.com/spreadsheets/d/SPREADSHEET_ID/edit
   ```
3. Save this ID for later configuration

---

## 📝 **Step 2: Create Google Apps Script**

### 2.1 Access Google Apps Script
1. Go to [Google Apps Script](https://script.google.com/)
2. Sign in with the same Google account used for the spreadsheet
3. Click "New Project"

### 2.2 Set Up the Script
1. Delete the default `myFunction()` code
2. Copy the entire code from `Google_Apps_Script_Code.gs` file
3. Paste it into the script editor

### 2.3 Configure the Script
1. Find these lines at the top of the script:
   ```javascript
   const SPREADSHEET_ID = 'YOUR_SPREADSHEET_ID_HERE';
   const API_KEY = 'YOUR_OPTIONAL_API_KEY_HERE';
   ```
2. Replace `YOUR_SPREADSHEET_ID_HERE` with your actual spreadsheet ID from Step 1.3
3. **Optional**: Set an API key for additional security, or leave as empty string `''`

### 2.4 Save the Script
1. Click "Save" (Ctrl+S)
2. Give your project a name: `Photography Booking Integration`

---

## 🚀 **Step 3: Deploy Google Apps Script as Web App**

### 3.1 Deploy the Script
1. In the Google Apps Script editor, click "Deploy" > "New deployment"
2. Click the gear icon ⚙️ next to "Type" and select "Web app"

### 3.2 Configure Deployment
1. **Description**: `Photography Booking System Integration`
2. **Execute as**: `Me (<EMAIL>)`
3. **Who has access**: `Anyone` (this allows your booking system to send data)
4. Click "Deploy"

### 3.3 Authorize the Script
1. Click "Authorize access"
2. Choose your Google account
3. Click "Advanced" if you see a warning
4. Click "Go to Photography Booking Integration (unsafe)"
5. Click "Allow"

### 3.4 Copy the Web App URL
1. After deployment, copy the "Web app URL"
2. It will look like: `https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec`
3. Save this URL for your booking system configuration

---

## ⚙️ **Step 4: Configure Your Booking System**

### 4.1 Create Environment File
1. In your project root, copy `.env.example` to `.env`
2. Open `.env` file in a text editor

### 4.2 Add Google Sheets Configuration
```env
# Enable Google Sheets Integration
VITE_GOOGLE_SHEETS_ENABLED=true

# Google Apps Script Configuration
VITE_GOOGLE_APPS_SCRIPT_URL=https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec

# Spreadsheet Configuration
VITE_GOOGLE_SHEETS_SPREADSHEET_ID=your_spreadsheet_id_here
VITE_GOOGLE_SHEETS_WORKSHEET_NAME=Bookings

# Optional API Key (if you set one in your Google Apps Script)
VITE_GOOGLE_SHEETS_API_KEY=your_optional_api_key
```

### 4.3 Fill in Your Values
1. **VITE_GOOGLE_APPS_SCRIPT_URL**: Use the Web App URL from Step 3.4
2. **VITE_GOOGLE_SHEETS_SPREADSHEET_ID**: Use the spreadsheet ID from Step 1.3
3. **VITE_GOOGLE_SHEETS_WORKSHEET_NAME**: Use `Bookings` (or your preferred worksheet name)
4. **VITE_GOOGLE_SHEETS_API_KEY**: Optional - use the API key you set in the script, or leave empty

---

## 🔒 **Step 5: Security Best Practices**

### 5.1 Environment Variables
- **Never commit `.env` file to version control**
- Add `.env` to your `.gitignore` file
- Use environment variables in production

### 5.2 Service Account Security
- Store JSON key file securely
- Don't share service account credentials
- Regularly rotate service account keys
- Use least privilege principle

### 5.3 Spreadsheet Access
- Only share with necessary team members
- Use "Editor" permission only when needed
- Consider using "Viewer" for read-only access
- Enable 2-factor authentication on Google account

---

## 🧪 **Step 6: Testing the Integration**

### 6.1 Development Testing
1. Start your development server: `npm run dev`
2. Fill out a test booking form
3. Submit via WhatsApp
4. Check your Google Sheets for the new row

### 6.2 Verify Data Structure
The system creates these columns automatically:
- Booking Reference
- Submission Time
- Event Type
- Package Name & Price
- Client Information (Names, Contact)
- Event Details (Dates, Venues, Times)
- Additional Notes

### 6.3 Troubleshooting
If data doesn't appear:
1. Check browser console for errors
2. Verify service account email has access to spreadsheet
3. Confirm all environment variables are set correctly
4. Test Google Sheets API connection

---

## 📈 **Step 7: Using Your Data**

### 7.1 Data Analysis
- Use Google Sheets pivot tables for booking analytics
- Create charts to visualize booking trends
- Filter data by event type, package, or date range

### 7.2 Team Collaboration
- Share spreadsheet with team members
- Use comments for booking notes
- Set up notifications for new bookings

### 7.3 Data Export
- Export to Excel, CSV, or PDF
- Connect to Google Data Studio for advanced reporting
- Use Google Apps Script for automation

---

## 🆘 **Troubleshooting Common Issues**

### Issue 1: "Authentication Error"
**Solution**: 
- Verify service account email has access to spreadsheet
- Check that private key is correctly formatted in .env file
- Ensure Google Sheets API is enabled

### Issue 2: "Spreadsheet Not Found"
**Solution**:
- Verify spreadsheet ID is correct
- Check that spreadsheet is shared with service account
- Confirm worksheet name matches configuration

### Issue 3: "Permission Denied"
**Solution**:
- Grant "Editor" permission to service account
- Verify service account has correct roles in Google Cloud
- Check that API quotas are not exceeded

### Issue 4: Data Not Appearing
**Solution**:
- Check browser console for JavaScript errors
- Verify VITE_GOOGLE_SHEETS_ENABLED=true
- Test with a simple booking submission

---

## 📞 **Support**

For technical support with Google Sheets integration:

**Tera Works - Professional Web Solutions**
- **WhatsApp**: +94 71 576 8552
- **Email**: <EMAIL>
- **Specialization**: Custom booking systems with Google Sheets integration

---

## 📝 **Configuration Checklist**

- [ ] Google Cloud project created
- [ ] Google Sheets API enabled
- [ ] Service account created with JSON key
- [ ] Google Sheets spreadsheet created and shared
- [ ] Environment variables configured
- [ ] Integration tested with sample booking
- [ ] Team members granted appropriate access
- [ ] Security best practices implemented

**Setup Complete!** Your booking system now automatically saves all submissions to Google Sheets.
