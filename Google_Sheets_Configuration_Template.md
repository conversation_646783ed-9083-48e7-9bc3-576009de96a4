# Google Sheets Configuration Template
## Quick Setup Reference for Photography Booking System

This template provides a quick reference for configuring Google Sheets integration. Follow the detailed setup guide first, then use this template for configuration.

---

## 📋 **Configuration Checklist**

### ✅ **Prerequisites Completed**
- [ ] Google Cloud project created
- [ ] Google Sheets API enabled  
- [ ] Service account created with <PERSON><PERSON><PERSON> key downloaded
- [ ] Google Sheets spreadsheet created
- [ ] Spreadsheet shared with service account email

---

## 🔧 **Environment Configuration**

### **Step 1: Copy Environment Template**
```bash
cp .env.example .env
```

### **Step 2: Edit .env File**
Open `.env` file and replace the placeholder values:

```env
# Google Sheets Integration
VITE_GOOGLE_SHEETS_ENABLED=true
VITE_GOOGLE_SHEETS_SPREADSHEET_ID=REPLACE_WITH_YOUR_SPREADSHEET_ID
VITE_GOOGLE_SHEETS_WORKSHEET_NAME=Bookings

# Google Service Account Credentials
VITE_GOOGLE_PROJECT_ID=REPLACE_WITH_PROJECT_ID
VITE_GOOGLE_PRIVATE_KEY_ID=REPLACE_WITH_PRIVATE_KEY_ID
VITE_GOOGLE_PRIVATE_KEY="REPLACE_WITH_PRIVATE_KEY"
VITE_GOOGLE_CLIENT_EMAIL=REPLACE_WITH_CLIENT_EMAIL
VITE_GOOGLE_CLIENT_ID=REPLACE_WITH_CLIENT_ID
VITE_GOOGLE_CLIENT_CERT_URL=REPLACE_WITH_CERT_URL
```

---

## 📊 **Spreadsheet Setup Template**

### **Spreadsheet Information**
- **Name**: `Photography Bookings - [Your Business Name]`
- **Worksheet Name**: `Bookings`
- **URL Format**: `https://docs.google.com/spreadsheets/d/SPREADSHEET_ID/edit`

### **Required Permissions**
- Service account email: `[service-account]@[project-id].iam.gserviceaccount.com`
- Permission level: **Editor**
- Notification: **Disabled**

---

## 🔑 **Service Account JSON Mapping**

Map values from your downloaded JSON file to environment variables:

| JSON Field | Environment Variable | Example |
|------------|---------------------|---------|
| `project_id` | `VITE_GOOGLE_PROJECT_ID` | `my-booking-system-123` |
| `private_key_id` | `VITE_GOOGLE_PRIVATE_KEY_ID` | `abc123def456...` |
| `private_key` | `VITE_GOOGLE_PRIVATE_KEY` | `"-----BEGIN PRIVATE KEY-----\n..."` |
| `client_email` | `VITE_GOOGLE_CLIENT_EMAIL` | `<EMAIL>` |
| `client_id` | `VITE_GOOGLE_CLIENT_ID` | `123456789012345678901` |
| `client_x509_cert_url` | `VITE_GOOGLE_CLIENT_CERT_URL` | `https://www.googleapis.com/robot/v1/metadata/x509/...` |

---

## 🧪 **Testing Configuration**

### **Quick Test Steps**
1. Start development server: `npm run dev`
2. Navigate to booking form
3. Fill out test booking with these details:
   ```
   Event Type: Engagement Session
   Package: Package 2
   Bride Name: Test Bride
   Groom Name: Test Groom
   Email: <EMAIL>
   Phone: +***********
   ```
4. Submit via WhatsApp
5. Check Google Sheets for new row

### **Expected Result**
- New row appears in Google Sheets
- All form data is populated correctly
- Booking reference is generated
- Submission timestamp is recorded

---

## 🔍 **Verification Checklist**

### **Environment Variables**
- [ ] All VITE_GOOGLE_* variables are set
- [ ] Private key includes proper line breaks (`\n`)
- [ ] No placeholder values remain
- [ ] .env file is in .gitignore

### **Google Cloud Setup**
- [ ] Google Sheets API is enabled
- [ ] Service account has correct permissions
- [ ] JSON key file is downloaded and secure

### **Spreadsheet Setup**
- [ ] Spreadsheet is created and accessible
- [ ] Service account email has Editor access
- [ ] Worksheet name matches configuration
- [ ] Spreadsheet ID is correctly extracted

### **Integration Testing**
- [ ] Test booking submission works
- [ ] Data appears in Google Sheets
- [ ] All columns are populated correctly
- [ ] No console errors in browser

---

## 🚨 **Common Configuration Errors**

### **Error 1: "Authentication failed"**
**Check**: 
- Service account email has access to spreadsheet
- Private key format is correct (includes `\n` characters)
- All credentials match JSON file exactly

### **Error 2: "Spreadsheet not found"**
**Check**:
- Spreadsheet ID is correct (from URL)
- Spreadsheet is shared with service account
- Worksheet name matches exactly

### **Error 3: "API not enabled"**
**Check**:
- Google Sheets API is enabled in Google Cloud Console
- Correct project is selected
- API quotas are not exceeded

---

## 📝 **Production Deployment Notes**

### **Environment Variables in Production**
- Use your hosting platform's environment variable settings
- Never commit .env file to version control
- Use secure environment variable storage

### **Popular Hosting Platforms**
- **Vercel**: Add variables in Project Settings > Environment Variables
- **Netlify**: Add variables in Site Settings > Environment Variables  
- **Heroku**: Use `heroku config:set` command
- **AWS**: Use Parameter Store or Secrets Manager

---

## 🔄 **Maintenance & Updates**

### **Regular Tasks**
- [ ] Monitor Google Sheets for data accuracy
- [ ] Check API usage quotas monthly
- [ ] Rotate service account keys annually
- [ ] Review spreadsheet access permissions quarterly

### **Backup Recommendations**
- Export spreadsheet data monthly
- Keep service account JSON file secure
- Document configuration for team members

---

## 📞 **Quick Support Reference**

**For immediate assistance:**
- **Setup Issues**: Review detailed setup guide
- **Configuration Errors**: Check verification checklist above
- **Technical Support**: Contact Tera Works (+94 71 576 8552)

**Useful Links:**
- [Google Cloud Console](https://console.cloud.google.com/)
- [Google Sheets](https://sheets.google.com/)
- [Google Sheets API Documentation](https://developers.google.com/sheets/api)

---

**Configuration Template Complete!** 
Use this template alongside the detailed setup guide for quick reference during configuration.
