# Google Sheets Integration Configuration
# Copy this file to .env and fill in your actual values

# Enable/disable Google Sheets integration
VITE_GOOGLE_SHEETS_ENABLED=true

# Google Apps Script Webhook Configuration
# Deploy your Google Apps Script as a web app and get the URL
VITE_GOOGLE_APPS_SCRIPT_URL=https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec

# Google Sheets Spreadsheet Configuration
VITE_GOOGLE_SHEETS_SPREADSHEET_ID=your_spreadsheet_id_here
VITE_GOOGLE_SHEETS_WORKSHEET_NAME=Bookings

# Optional API key for additional security (set in your Google Apps Script)
VITE_GOOGLE_SHEETS_API_KEY=your_optional_api_key

# EmailJS Configuration (existing)
VITE_EMAILJS_PUBLIC_KEY=pyjj4z90GoTsyHDQT
VITE_EMAILJS_SERVICE_ID=service_ywznx8m
VITE_EMAILJS_TEMPLATE_ID=template_rae6acc
