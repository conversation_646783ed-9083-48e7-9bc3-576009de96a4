/**
 * Google Apps Script for Photography Booking System - Google Sheets Integration
 * 
 * This script receives webhook requests from the booking system and appends data to Google Sheets.
 * 
 * Setup Instructions:
 * 1. Open Google Apps Script (script.google.com)
 * 2. Create a new project
 * 3. Replace the default code with this script
 * 4. Configure the SPREADSHEET_ID and API_KEY constants below
 * 5. Deploy as a web app with execute permissions for "Anyone"
 * 6. Copy the web app URL to your booking system configuration
 */

// Configuration - Update these values
const SPREADSHEET_ID = 'YOUR_SPREADSHEET_ID_HERE'; // Replace with your actual spreadsheet ID
const API_KEY = 'YOUR_OPTIONAL_API_KEY_HERE'; // Optional: Set to empty string '' if not using
const DEFAULT_WORKSHEET_NAME = 'Bookings';

// Column headers for the spreadsheet
const HEADERS = [
  'Booking Reference',
  'Submission Time',
  'Event Type',
  'Package Name',
  'Package Price',
  'Bride Name',
  'Groom Name',
  'Phone Number',
  'Email',
  'Event Date',
  'Venue',
  'Guest Count',
  'Makeup Artist',
  'Additional Notes',
  'How Heard About Us',
  'Registration Time',
  'Ring Exchange Time',
  'Ceremony Time',
  'Event End Time',
  'Primary Location',
  'Wedding Date',
  'Homecoming Date',
  'Pre-Shoot Date',
  'Outfit Changes',
  'Desired Location',
  'Wedding Venue',
  'Wedding Guest Count',
  'Homecoming Venue',
  'Homecoming Guest Count',
  'Homecoming End Time'
];

/**
 * Main function to handle POST requests from the booking system
 */
function doPost(e) {
  try {
    // Parse the request data
    const data = JSON.parse(e.postData.contents);
    
    // Validate API key if configured
    if (API_KEY && API_KEY !== '' && data.apiKey !== API_KEY) {
      return ContentService
        .createTextOutput(JSON.stringify({
          success: false,
          error: 'Invalid API key'
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    // Handle different actions
    switch (data.action) {
      case 'testConnection':
        return handleTestConnection(data);
      case 'appendBookingData':
        return handleAppendBookingData(data);
      default:
        return ContentService
          .createTextOutput(JSON.stringify({
            success: false,
            error: 'Unknown action: ' + data.action
          }))
          .setMimeType(ContentService.MimeType.JSON);
    }
    
  } catch (error) {
    console.error('Error in doPost:', error);
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: 'Server error: ' + error.message
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * Handle test connection requests
 */
function handleTestConnection(data) {
  try {
    const spreadsheet = SpreadsheetApp.openById(data.spreadsheetId || SPREADSHEET_ID);
    const title = spreadsheet.getName();
    
    return ContentService
      .createTextOutput(JSON.stringify({
        success: true,
        message: 'Connection successful',
        spreadsheetTitle: title,
        timestamp: new Date().toISOString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: 'Connection failed: ' + error.message
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * Handle booking data append requests
 */
function handleAppendBookingData(data) {
  try {
    const spreadsheetId = data.spreadsheetId || SPREADSHEET_ID;
    const worksheetName = data.worksheetName || DEFAULT_WORKSHEET_NAME;
    const bookingData = data.data;
    
    // Open the spreadsheet
    const spreadsheet = SpreadsheetApp.openById(spreadsheetId);
    
    // Get or create the worksheet
    let worksheet;
    try {
      worksheet = spreadsheet.getSheetByName(worksheetName);
    } catch (error) {
      worksheet = spreadsheet.insertSheet(worksheetName);
    }
    
    // Ensure headers exist
    ensureHeaders(worksheet);
    
    // Prepare row data
    const rowData = [
      bookingData.bookingReference || '',
      bookingData.submissionTime || new Date().toLocaleString(),
      bookingData.eventType || '',
      bookingData.packageName || '',
      bookingData.packagePrice || '',
      bookingData.brideName || '',
      bookingData.groomName || '',
      bookingData.phoneNumber || '',
      bookingData.email || '',
      bookingData.eventDate || '',
      bookingData.venue || '',
      bookingData.guestCount || '',
      bookingData.makeupArtist || '',
      bookingData.additionalNotes || '',
      bookingData.hearAbout || '',
      bookingData.registrationTime || '',
      bookingData.ringExchangeTime || '',
      bookingData.ceremonyTime || '',
      bookingData.eventEndTime || '',
      bookingData.primaryLocation || '',
      bookingData.weddingDate || '',
      bookingData.homecomingDate || '',
      bookingData.preShootDate || '',
      bookingData.outfitChanges || '',
      bookingData.desiredLocation || '',
      bookingData.weddingVenue || '',
      bookingData.weddingGuestCount || '',
      bookingData.homecomingVenue || '',
      bookingData.homecomingGuestCount || '',
      bookingData.homecomingEndTime || ''
    ];
    
    // Append the data
    worksheet.appendRow(rowData);
    
    // Get the row number that was just added
    const lastRow = worksheet.getLastRow();
    
    return ContentService
      .createTextOutput(JSON.stringify({
        success: true,
        message: 'Booking data added successfully',
        rowNumber: lastRow,
        bookingReference: bookingData.bookingReference,
        timestamp: new Date().toISOString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    console.error('Error appending booking data:', error);
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: 'Failed to append data: ' + error.message
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * Ensure the worksheet has proper headers
 */
function ensureHeaders(worksheet) {
  const firstRow = worksheet.getRange(1, 1, 1, HEADERS.length).getValues()[0];
  
  // Check if headers are missing or incomplete
  let needsHeaders = false;
  for (let i = 0; i < HEADERS.length; i++) {
    if (firstRow[i] !== HEADERS[i]) {
      needsHeaders = true;
      break;
    }
  }
  
  if (needsHeaders) {
    // Clear the first row and add headers
    worksheet.getRange(1, 1, 1, HEADERS.length).setValues([HEADERS]);
    
    // Format headers
    const headerRange = worksheet.getRange(1, 1, 1, HEADERS.length);
    headerRange.setFontWeight('bold');
    headerRange.setBackground('#4285f4');
    headerRange.setFontColor('#ffffff');
    
    // Auto-resize columns
    for (let i = 1; i <= HEADERS.length; i++) {
      worksheet.autoResizeColumn(i);
    }
  }
}

/**
 * Handle GET requests (for testing purposes)
 */
function doGet(e) {
  return ContentService
    .createTextOutput(JSON.stringify({
      message: 'Photography Booking System - Google Sheets Integration',
      status: 'Active',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    }))
    .setMimeType(ContentService.MimeType.JSON);
}

/**
 * Test function for manual execution
 */
function testScript() {
  const testData = {
    action: 'appendBookingData',
    spreadsheetId: SPREADSHEET_ID,
    worksheetName: DEFAULT_WORKSHEET_NAME,
    data: {
      bookingReference: 'TEST-' + new Date().getTime(),
      submissionTime: new Date().toLocaleString(),
      eventType: 'Test Event',
      packageName: 'Test Package',
      packagePrice: '50,000.00 LKR',
      brideName: 'Test Bride',
      groomName: 'Test Groom',
      phoneNumber: '+94123456789',
      email: '<EMAIL>',
      eventDate: '2024-12-31',
      venue: 'Test Venue',
      guestCount: '100',
      makeupArtist: 'Test Artist',
      additionalNotes: 'This is a test booking',
      hearAbout: 'Test Source'
    }
  };
  
  const result = handleAppendBookingData(testData);
  console.log('Test result:', result.getContent());
}
