import { GoogleSheetsConfig } from '../utils/googleSheetsService';

// Google Sheets Configuration using Google Apps Script Webhook
// IMPORTANT: Replace these values with your actual Google Apps Script webhook configuration
export const GOOGLE_SHEETS_CONFIG: GoogleSheetsConfig = {
  // Your Google Apps Script webhook URL
  // Example: https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec
  webhookUrl: process.env.VITE_GOOGLE_APPS_SCRIPT_URL || 'YOUR_GOOGLE_APPS_SCRIPT_URL_HERE',

  // Your Google Sheets spreadsheet ID (found in the URL)
  // Example: https://docs.google.com/spreadsheets/d/SPREADSHEET_ID/edit
  spreadsheetId: process.env.VITE_GOOGLE_SHEETS_SPREADSHEET_ID || 'YOUR_SPREADSHEET_ID_HERE',

  // The name of the worksheet/tab where data should be appended
  worksheetName: process.env.VITE_GOOGLE_SHEETS_WORKSHEET_NAME || 'Bookings',

  // Optional API key for additional security
  apiKey: process.env.VITE_GOOGLE_SHEETS_API_KEY || undefined
};

// Feature flag to enable/disable Google Sheets integration
export const GOOGLE_SHEETS_ENABLED = process.env.VITE_GOOGLE_SHEETS_ENABLED === 'true';

// Validation function to check if Google Sheets is properly configured
export function validateGoogleSheetsConfig(): boolean {
  const config = GOOGLE_SHEETS_CONFIG;

  // Check if all required fields are present and not default values
  const requiredFields = [
    config.webhookUrl !== 'YOUR_GOOGLE_APPS_SCRIPT_URL_HERE',
    config.spreadsheetId !== 'YOUR_SPREADSHEET_ID_HERE',
    config.webhookUrl.includes('script.google.com') || config.webhookUrl.includes('localhost') // Allow localhost for testing
  ];

  const isValid = requiredFields.every(field => field);

  if (!isValid) {
    console.warn('Google Sheets configuration is incomplete. Please update the configuration with your actual Google Apps Script webhook URL and spreadsheet ID.');
  }

  return isValid;
}

// Development/Demo configuration (for testing purposes)
export const DEMO_GOOGLE_SHEETS_CONFIG: GoogleSheetsConfig = {
  webhookUrl: 'https://script.google.com/macros/s/DEMO_SCRIPT_ID/exec',
  spreadsheetId: 'DEMO_SPREADSHEET_ID',
  worksheetName: 'Demo Bookings',
  apiKey: 'demo-api-key'
};

// Helper function to get the appropriate configuration
export function getGoogleSheetsConfig(): GoogleSheetsConfig {
  // In development mode, you can switch to demo config for testing
  if (import.meta.env.DEV && !validateGoogleSheetsConfig()) {
    console.log('Using demo Google Sheets configuration for development');
    return DEMO_GOOGLE_SHEETS_CONFIG;
  }
  
  return GOOGLE_SHEETS_CONFIG;
}
