# Photography Booking System with Google Sheets Integration

A professional booking system for photographers with automatic Google Sheets data logging, built with React, TypeScript, and Tailwind CSS.

## 🌟 Features

### Core Booking System
- **Multi-Event Support**: Engagement sessions, weddings, homecoming celebrations
- **Package Management**: Customizable packages with detailed pricing
- **Professional Forms**: Step-by-step booking process with validation
- **Email Integration**: Automatic email notifications via EmailJS
- **WhatsApp Integration**: Pre-filled WhatsApp messages for instant communication
- **PDF Generation**: Downloadable booking summaries
- **Mobile Responsive**: Optimized for all device sizes

### Google Sheets Integration
- **Automatic Data Logging**: All bookings saved to Google Sheets instantly
- **Real-time Updates**: Data appears immediately after form submission
- **Structured Data**: Organized columns for easy analysis and reporting
- **Browser Compatible**: Uses Google Apps Script webhook (no server required)
- **Error Handling**: Graceful fallback if Google Sheets is unavailable
- **Configurable**: Easy setup with environment variables

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Google account for Google Sheets integration
- EmailJS account for email functionality

### Installation

1. **Clone or download this template**
   ```bash
   # This is a standalone template (not connected to GitHub)
   cd "Booking Template - Google Sheets Integration"
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your actual configuration
   ```

4. **Set up Google Sheets integration** (see detailed guide below)

5. **Start development server**
   ```bash
   npm run dev
   ```

6. **Build for production**
   ```bash
   npm run build
   ```

## 📊 Google Sheets Integration Setup

### Step 1: Create Google Sheets Spreadsheet
1. Go to [Google Sheets](https://sheets.google.com/)
2. Create a new spreadsheet
3. Rename it to: `Photography Bookings - [Your Business Name]`
4. Rename the first sheet to: `Bookings`
5. Copy the spreadsheet ID from the URL

### Step 2: Deploy Google Apps Script
1. Go to [Google Apps Script](https://script.google.com/)
2. Create a new project
3. Copy the code from `Google_Apps_Script_Code.gs`
4. Configure the `SPREADSHEET_ID` in the script
5. Deploy as a web app with "Anyone" access
6. Copy the web app URL

### Step 3: Configure Your Booking System
Update your `.env` file:
```env
VITE_GOOGLE_SHEETS_ENABLED=true
VITE_GOOGLE_APPS_SCRIPT_URL=https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec
VITE_GOOGLE_SHEETS_SPREADSHEET_ID=your_spreadsheet_id_here
VITE_GOOGLE_SHEETS_WORKSHEET_NAME=Bookings
```

## 📁 Project Structure

```
src/
├── components/
│   ├── ValueProposition.tsx    # Feature showcase component
│   └── ui/                     # Reusable UI components
├── pages/
│   └── Booking.tsx            # Main booking form
├── utils/
│   ├── googleSheetsService.ts # Google Sheets integration
│   ├── whatsapp.ts           # WhatsApp message generation
│   ├── pdfGenerator.ts       # PDF creation utilities
│   └── emailjsTest.ts        # Email testing utilities
├── config/
│   └── googleSheetsConfig.ts # Google Sheets configuration
└── data/
    └── packages.ts           # Package and pricing data
```

## 🔧 Configuration

### Environment Variables

```env
# Google Sheets Integration
VITE_GOOGLE_SHEETS_ENABLED=true
VITE_GOOGLE_APPS_SCRIPT_URL=your_google_apps_script_url
VITE_GOOGLE_SHEETS_SPREADSHEET_ID=your_spreadsheet_id
VITE_GOOGLE_SHEETS_WORKSHEET_NAME=Bookings
VITE_GOOGLE_SHEETS_API_KEY=optional_api_key

# EmailJS Configuration
VITE_EMAILJS_PUBLIC_KEY=your_emailjs_public_key
VITE_EMAILJS_SERVICE_ID=your_emailjs_service_id
VITE_EMAILJS_TEMPLATE_ID=your_emailjs_template_id
```

### Package Configuration

Edit `src/data/packages.ts` to customize:
- Event types and packages
- Pricing and features
- Package descriptions
- Form fields for each event type

## 📋 Data Structure

The Google Sheets integration captures:

| Column | Description |
|--------|-------------|
| Booking Reference | Unique booking identifier |
| Submission Time | When the booking was submitted |
| Event Type | Type of photography session |
| Package Name | Selected package |
| Package Price | Package pricing |
| Client Information | Names, contact details |
| Event Details | Dates, venues, times |
| Additional Notes | Special requests |

## 🧪 Testing

### Test Google Sheets Integration
1. Enable development mode: `npm run dev`
2. Fill out a test booking form
3. Submit via WhatsApp
4. Check your Google Sheets for the new row

### Test Email Functionality
1. Configure EmailJS credentials in `.env`
2. Submit a test booking
3. Check email delivery

## 🔒 Security Considerations

### Google Apps Script Security
- Deploy with "Anyone" access (required for webhook)
- Use optional API key for additional security
- Regularly review Google Apps Script logs

### Environment Variables
- Never commit `.env` file to version control
- Use secure environment variable storage in production
- Rotate API keys regularly

## 📚 Documentation

- **Setup Guide**: `Google_Sheets_Integration_Setup_Guide.md`
- **Configuration Template**: `Google_Sheets_Configuration_Template.md`
- **Testing Guide**: `Google_Sheets_Testing_Guide.md`
- **Google Apps Script Code**: `Google_Apps_Script_Code.gs`

## 🚀 Deployment

### Popular Hosting Platforms

**Vercel**
```bash
npm run build
# Deploy dist/ folder to Vercel
# Add environment variables in Vercel dashboard
```

**Netlify**
```bash
npm run build
# Deploy dist/ folder to Netlify
# Add environment variables in Netlify settings
```

**Traditional Hosting**
```bash
npm run build
# Upload dist/ folder to your web server
```

## 🆘 Troubleshooting

### Common Issues

**Google Sheets not updating**
- Check Google Apps Script deployment URL
- Verify spreadsheet ID is correct
- Check browser console for errors

**Email not sending**
- Verify EmailJS credentials
- Check EmailJS dashboard for quota limits
- Test with different email addresses

**Build errors**
- Clear node_modules: `rm -rf node_modules && npm install`
- Update dependencies: `npm update`
- Check TypeScript errors: `npm run type-check`

## 📞 Support

**Tera Works - Professional Web Solutions**
- **WhatsApp**: +94 71 576 8552
- **Email**: <EMAIL>
- **Specialization**: Custom booking systems with Google Sheets integration

## 📄 License

This is a commercial template provided by Tera Works. Customize and deploy for your photography business.

---

**Built with ❤️ by Tera Works**  
*Let's Grow Together*
