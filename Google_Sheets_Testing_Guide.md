# Google Sheets Integration Testing & Troubleshooting Guide
## Photography Booking System

This guide provides comprehensive testing procedures and troubleshooting solutions for Google Sheets integration.

---

## 🧪 **Testing Procedures**

### **Test 1: Basic Connection Test**

**Purpose**: Verify Google Sheets API connection and authentication

**Steps**:
1. Open browser developer tools (F12)
2. Navigate to booking form
3. Check console for initialization messages:
   ```
   ✅ Expected: "Google Sheets service initialized successfully"
   ❌ Error: "Failed to initialize Google Sheets service"
   ```

**Expected Result**: No authentication errors in console

---

### **Test 2: Engagement Session Booking**

**Purpose**: Test complete booking flow with engagement package

**Test Data**:
```
Event Type: Engagement Session
Package: Package 2 - 55,000.00 LKR
Bride Name: <PERSON>room Name: <PERSON>
Engagement Date: 2024-03-15
Registration Time: 14:00
Ring Exchange Time: 15:30
Event End Time: 20:00
Venue: Grand Hotel Colombo
Guest Count: 75
Makeup Artist: <PERSON>
Phone: +94771234567
Email: <EMAIL>
How heard about us: Social Media
```

**Expected Google Sheets Result**:
- New row with all data populated
- Booking reference generated (format: ENG-YYYYMMDD-XXXX)
- Submission timestamp recorded
- All event-specific fields filled

---

### **Test 3: Wedding Day Booking**

**Purpose**: Test wedding-specific fields and data mapping

**Test Data**:
```
Event Type: Wedding Day
Package: Package 3 - 100,000.00 LKR
Bride Name: Emma Wilson
Groom Name: James Brown
Wedding Date: 2024-04-20
Ceremony Time: 16:00
Event End Time: 23:00
Venue: Cinnamon Grand Hotel
Primary Location: Galle Face Green
Guest Count: 150
Makeup Artist: Priya Fernando
Phone: +94712345678
Email: <EMAIL>
```

**Expected Result**: Wedding-specific columns populated correctly

---

### **Test 4: Error Handling Test**

**Purpose**: Verify system handles Google Sheets errors gracefully

**Steps**:
1. Temporarily disable Google Sheets (set `VITE_GOOGLE_SHEETS_ENABLED=false`)
2. Submit booking form
3. Verify email still sends successfully
4. Check for appropriate console messages

**Expected Result**: 
- Email submission continues to work
- WhatsApp integration unaffected
- Console shows: "Google Sheets integration is disabled"

---

## 🔧 **Troubleshooting Guide**

### **Issue 1: Authentication Errors**

**Symptoms**:
- Console error: "Google Sheets authentication failed"
- Error: "invalid_grant" or "unauthorized_client"

**Solutions**:
1. **Check Service Account Email**:
   ```bash
   # Verify in .env file
   VITE_GOOGLE_CLIENT_EMAIL=<EMAIL>
   ```

2. **Verify Spreadsheet Sharing**:
   - Open Google Sheets
   - Click "Share" button
   - Confirm service account email has "Editor" access

3. **Check Private Key Format**:
   ```env
   # Correct format with \n characters
   VITE_GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----\n"
   ```

4. **Regenerate Service Account Key**:
   - Go to Google Cloud Console
   - IAM & Admin > Service Accounts
   - Create new key for existing service account

---

### **Issue 2: Spreadsheet Not Found**

**Symptoms**:
- Error: "The caller does not have permission"
- Error: "Requested entity was not found"

**Solutions**:
1. **Verify Spreadsheet ID**:
   ```
   URL: https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit
   ID:  1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms
   ```

2. **Check Worksheet Name**:
   ```env
   VITE_GOOGLE_SHEETS_WORKSHEET_NAME=Bookings
   ```
   Must match exactly (case-sensitive)

3. **Verify Spreadsheet Access**:
   - Open spreadsheet in browser
   - Confirm you can edit it
   - Check sharing settings

---

### **Issue 3: API Quota Exceeded**

**Symptoms**:
- Error: "Quota exceeded for quota metric"
- Intermittent failures during high usage

**Solutions**:
1. **Check API Usage**:
   - Google Cloud Console > APIs & Services > Quotas
   - Monitor Google Sheets API usage

2. **Implement Rate Limiting**:
   ```javascript
   // Add delay between requests if needed
   await new Promise(resolve => setTimeout(resolve, 1000));
   ```

3. **Request Quota Increase**:
   - Google Cloud Console > APIs & Services > Quotas
   - Request increase for Google Sheets API

---

### **Issue 4: Data Not Appearing**

**Symptoms**:
- No errors in console
- Email works but no Google Sheets data

**Solutions**:
1. **Check Feature Flag**:
   ```env
   VITE_GOOGLE_SHEETS_ENABLED=true
   ```

2. **Verify Configuration Validation**:
   ```javascript
   // Check browser console for:
   console.log('Google Sheets configuration is incomplete');
   ```

3. **Test Connection Manually**:
   ```javascript
   // Add to browser console:
   const service = new GoogleSheetsService(getGoogleSheetsConfig());
   service.testConnection();
   ```

---

### **Issue 5: Partial Data Missing**

**Symptoms**:
- Some columns empty in Google Sheets
- Data appears truncated

**Solutions**:
1. **Check Form Data Mapping**:
   - Verify all form fields are captured
   - Check `transformFormDataForSheets` function

2. **Review Column Headers**:
   - Ensure header row exists
   - Verify column count matches data

3. **Test Different Event Types**:
   - Test engagement, wedding, homecoming separately
   - Check event-specific field mapping

---

## 📊 **Data Validation Tests**

### **Test Data Integrity**

**Verification Points**:
- [ ] Booking reference format: `[TYPE]-YYYYMMDD-XXXX`
- [ ] Timestamp format: Local date/time string
- [ ] Phone number format: Preserved as entered
- [ ] Email format: Valid email address
- [ ] Date format: YYYY-MM-DD
- [ ] Time format: HH:MM (24-hour)
- [ ] Currency format: "XX,XXX.XX LKR"

### **Test Special Characters**

**Test Cases**:
- Names with apostrophes: "O'Connor"
- Unicode characters: Sinhala text
- Special symbols: "&", "@", "#"
- Long text fields: Additional notes

**Expected Result**: All characters preserved correctly

---

## 🔍 **Debugging Tools**

### **Browser Console Commands**

```javascript
// Test Google Sheets configuration
console.log(getGoogleSheetsConfig());

// Check feature flag
console.log('Sheets enabled:', GOOGLE_SHEETS_ENABLED);

// Validate configuration
console.log('Config valid:', validateGoogleSheetsConfig());

// Test service initialization
const service = new GoogleSheetsService(getGoogleSheetsConfig());
service.testConnection().then(result => console.log('Connection test:', result));
```

### **Network Tab Monitoring**

**Look for**:
- Requests to `sheets.googleapis.com`
- HTTP status codes (200 = success, 401 = auth error, 403 = permission error)
- Response times and payload sizes

---

## 📈 **Performance Testing**

### **Load Testing**

**Test Scenarios**:
1. **Single Submission**: Normal booking flow
2. **Multiple Rapid Submissions**: Test rate limiting
3. **Large Data Sets**: Long notes, many guests
4. **Concurrent Users**: Multiple simultaneous bookings

**Performance Metrics**:
- Google Sheets write time: < 3 seconds
- Total submission time: < 10 seconds
- Error rate: < 1%

---

## 🚨 **Emergency Procedures**

### **If Google Sheets Fails Completely**

1. **Immediate Action**:
   ```env
   VITE_GOOGLE_SHEETS_ENABLED=false
   ```

2. **Verify Email Still Works**: Test booking submission

3. **Investigate Issue**: Check Google Cloud Console for outages

4. **Temporary Solution**: Export existing data, fix configuration

5. **Re-enable**: Set `VITE_GOOGLE_SHEETS_ENABLED=true` after fix

### **Data Recovery**

**If Data is Lost**:
1. Check Google Sheets revision history
2. Look for backup exports
3. Check email records for booking references
4. Contact Google Support if necessary

---

## 📞 **Support Escalation**

**Level 1**: Self-troubleshooting using this guide
**Level 2**: Check Google Cloud Console and API documentation
**Level 3**: Contact Tera Works technical support (+94 71 576 8552)
**Level 4**: Google Cloud Support (for API issues)

---

**Testing Complete!** Your Google Sheets integration is now thoroughly tested and ready for production use.
